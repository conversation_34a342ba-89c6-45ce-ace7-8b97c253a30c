../../bin/ct2-fairseq-converter.exe,sha256=EPhWkUdaXcvCXckJljZXn85HAjQIzNG0L_ImqcgF62w,108467
../../bin/ct2-marian-converter.exe,sha256=8BqvMmJ8hZVKORMpHvC_8AdS_4672_h8Slh4gAGNR_k,108466
../../bin/ct2-openai-gpt2-converter.exe,sha256=dGn1QulcBr3nYU5nFFj68SqxV4mbsGbs8kRU10wRUkY,108471
../../bin/ct2-opennmt-py-converter.exe,sha256=VjT0ObbGy5gSynxA1bh9cdMe-1B990p8YLEpBZUT2N0,108470
../../bin/ct2-opennmt-tf-converter.exe,sha256=VIwcAR7qgV4oekl22BYymin3Tozi_CdvY9CkXsH29L8,108470
../../bin/ct2-opus-mt-converter.exe,sha256=B1IuvZVSDAJeppCXLnFzkCGSmfNsdXoJ6aW27iyUYaU,108467
../../bin/ct2-transformers-converter.exe,sha256=lWRbraL1h2MpnohYBjR1ZnKmTH0U-oU5Uxxe6Wcz-G0,108472
ctranslate2-4.5.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
ctranslate2-4.5.0.dist-info/METADATA,sha256=qlqqdRYejTkwkS0xPpLQ92oL6570yU0WiQtBRlHiRyc,10207
ctranslate2-4.5.0.dist-info/RECORD,,
ctranslate2-4.5.0.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
ctranslate2-4.5.0.dist-info/WHEEL,sha256=5JPYeYl5ZdvdSkrGS4u21mmpPzpFx42qrXOSIgWf4pg,102
ctranslate2-4.5.0.dist-info/entry_points.txt,sha256=ZHkojut_TmVRHl0bJIGm2b9wqr98GAJqxN9rlJtQshs,466
ctranslate2-4.5.0.dist-info/top_level.txt,sha256=1hUaWzcFIuSo2BAIUHFA3Osgsu6S1giq0y6Rosv8HOQ,12
ctranslate2/__init__.py,sha256=HmklwU3BCnUgQAAGQeCBOr44QS5X0npr_WDkBh2e6Lo,1508
ctranslate2/__pycache__/__init__.cpython-310.pyc,,
ctranslate2/__pycache__/extensions.cpython-310.pyc,,
ctranslate2/__pycache__/logging.cpython-310.pyc,,
ctranslate2/__pycache__/version.cpython-310.pyc,,
ctranslate2/_ext.cp310-win_amd64.pyd,sha256=LIszGag7OyGBc3IlXfC5SkrY7JeSJRJrY12xoPs0Wss,723456
ctranslate2/converters/__init__.py,sha256=ufYjcXf2sK4fiXAUU6tIJyWmNuLjKFf_KH3GWLXe4ls,507
ctranslate2/converters/__pycache__/__init__.cpython-310.pyc,,
ctranslate2/converters/__pycache__/converter.cpython-310.pyc,,
ctranslate2/converters/__pycache__/fairseq.cpython-310.pyc,,
ctranslate2/converters/__pycache__/marian.cpython-310.pyc,,
ctranslate2/converters/__pycache__/openai_gpt2.cpython-310.pyc,,
ctranslate2/converters/__pycache__/opennmt_py.cpython-310.pyc,,
ctranslate2/converters/__pycache__/opennmt_tf.cpython-310.pyc,,
ctranslate2/converters/__pycache__/opus_mt.cpython-310.pyc,,
ctranslate2/converters/__pycache__/transformers.cpython-310.pyc,,
ctranslate2/converters/__pycache__/utils.cpython-310.pyc,,
ctranslate2/converters/converter.py,sha256=Qkb8NGLLmgqMT6HZkFq61zwbxyq3NlWcaxLZ6Ap-YOQ,3601
ctranslate2/converters/fairseq.py,sha256=uQpd-ftYSO4c6WdEwCUyuZWhzWX1UTG7dGOC6EtcDVE,12765
ctranslate2/converters/marian.py,sha256=1_7P3EbIDPOdyJbtb_Lp-LCBPBb9A8E9OhzoyFwTb64,11274
ctranslate2/converters/openai_gpt2.py,sha256=1rXKM2ZURZHWRv4XZ135fPkVWpM4rTG-q7VR7OD6d-A,3304
ctranslate2/converters/opennmt_py.py,sha256=Vva60az6tGqlQXs0UgC09r_fCD3u2u6wUJB-8V4OUFQ,13183
ctranslate2/converters/opennmt_tf.py,sha256=kfewOiHPookTrWKwl56K69njkGTcb7R4mNd5MAiM7xQ,16180
ctranslate2/converters/opus_mt.py,sha256=5KbPaTiBhhorPzMpTugIfIJ8SgcqHfJUbJrWKBN-Djs,1254
ctranslate2/converters/transformers.py,sha256=sUnUaxPYbY3SvxmFGqFlmfZSTFjn0aNKvR6sNAE5ctI,103269
ctranslate2/converters/utils.py,sha256=NESgQml7RvSvGD6RNodHgXt5VGTr0dset2gY11L-Y6w,3767
ctranslate2/ctranslate2.dll,sha256=221dcEvVrtoiRQQD-hGAXRTgWwzap_2UceqnVoZYDfE,60922368
ctranslate2/cudnn64_9.dll,sha256=wHzEfy-kpWZZPHr0qn5X7fCamFoP3dFMuNb0VuJSrwU,438840
ctranslate2/extensions.py,sha256=axO2FI8ddiFmlko2AzQ6VcdtF-3hDA7VmPGnTIkrPkI,21782
ctranslate2/libiomp5md.dll,sha256=C2O3Lj2yJYPGTyH0Z1c4FSrflnSzxHARp6y0dE3H2ZI,2030632
ctranslate2/logging.py,sha256=P9evHdxuMx_iHvwJjEASEq-j5062H64Pl5-fJjxEuHk,1221
ctranslate2/models/__init__.py,sha256=53p98uemtuvVPz8xK7_LbOhBiUJJu-c-NdmOHJgdXus,497
ctranslate2/models/__pycache__/__init__.cpython-310.pyc,,
ctranslate2/specs/__init__.py,sha256=9GabtSyczznYqiqUS6XvULi8pQ3_3RNRogXobGP0G80,653
ctranslate2/specs/__pycache__/__init__.cpython-310.pyc,,
ctranslate2/specs/__pycache__/attention_spec.cpython-310.pyc,,
ctranslate2/specs/__pycache__/common_spec.cpython-310.pyc,,
ctranslate2/specs/__pycache__/model_spec.cpython-310.pyc,,
ctranslate2/specs/__pycache__/transformer_spec.cpython-310.pyc,,
ctranslate2/specs/__pycache__/wav2vec2_spec.cpython-310.pyc,,
ctranslate2/specs/__pycache__/wav2vec2bert_spec.cpython-310.pyc,,
ctranslate2/specs/__pycache__/whisper_spec.cpython-310.pyc,,
ctranslate2/specs/attention_spec.py,sha256=ios3aZRWbZ8PmcYi9pXIad52lMweqOUgV5ZJbkFOKmE,3218
ctranslate2/specs/common_spec.py,sha256=freTDhQMy5PYofBrij4_FDgrKokMYApWSPIpASZIlJc,1608
ctranslate2/specs/model_spec.py,sha256=atCAYzDEIzyJ1TCayFGZVutHqSWa1ww-vbZ0OiIJqh8,25736
ctranslate2/specs/transformer_spec.py,sha256=vBTnBaZ8nslREF8FSJKP6VveyYAvS2_L0h8yqPAhpY0,30124
ctranslate2/specs/wav2vec2_spec.py,sha256=Wt1Qi_1uS0UfJaE0cS1QtHftrV2q8KnPWZuxrKhzmZ0,1834
ctranslate2/specs/wav2vec2bert_spec.py,sha256=dWmP4i5jbBtMcBMSPeX9JCnFLvXHaQG2hnj6_0Ldf1U,3295
ctranslate2/specs/whisper_spec.py,sha256=_vm1sc5yOowOJ4iyvcxMXrgt-UcLJrZT8OtPscUXcQQ,2447
ctranslate2/version.py,sha256=IxCD63yt8VxssLxeCkJ9SjM7Ej4ct18yY54CC-XeqCQ,53
