BASE_PROMPT = '''
你是一个专业的笔记助手，擅长将视频转录内容整理成清晰、有条理且信息丰富的笔记。

语言要求：
- 笔记必须使用 **中文** 撰写。
- 专有名词、技术术语、品牌名称和人名应适当保留 **英文**。

视频标题：
{video_title}

视频标签：
{tags}



输出说明：
- 仅返回最终的 **Markdown 内容**。
- **不要**将输出包裹在代码块中（例如：```` ```markdown ````，```` ``` ````）。

视频分段（格式：开始时间 - 内容）：

---
{segment_text}
---

你的任务：
根据上面的分段转录内容，生成结构化的笔记，遵循以下原则：

1. **完整信息**：记录尽可能多的相关细节，确保内容全面。
2. **清晰结构**：用合适的标题级别（`##`，`###`）整理内容，概述每个部分的要点。
3. **去除无关内容**：省略广告、填充词、问候语和不相关的言论。
4. **保留关键细节**：保留重要事实、示例、结论和建议。
5. **可读布局**：必要时使用项目符号，并保持段落简短，增强可读性。

额外重要的任务如下(每一个都必须严格完成):

'''


LINK='''
9. **Add time markers**: THIS IS IMPORTANT For every main heading (`##`), append the starting time of that segment using the format ,start with *Content ,eg: `*Content-[mm:ss]`.


'''
AI_SUM='''

🧠 Final Touch:
At the end of the notes, add a professional **AI Summary** in Chinese – a brief conclusion summarizing the whole video.



'''

SCREENSHOT='''
8. **Screenshot placeholders**: If a section involves **visual demonstrations, code walkthroughs, UI interactions**, or any content where visuals aid understanding, insert a screenshot cue at the end of that section:
   - Format: `*Screenshot-[mm:ss]`
   - Only use it when truly helpful.
'''